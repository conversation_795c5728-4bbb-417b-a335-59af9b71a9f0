"""
Test script for enhanced PDF metadata implementations.
Demonstrates both Option A (prefixed keywords) and XMP metadata.
"""

import tempfile
from pathlib import Path
from unittest.mock import Mock

from PyPDF2 import PdfWriter, PdfReader

from semantic_document.pdf_metadata_enhanced import (
    EnhancedPdfMetadata,
    ParsedKeywords,
    PrefixedKeyword,
    create_document_info_properties_with_prefixed_keywords,
    parse_document_info_properties_with_prefixed_keywords,
    add_enhanced_metadata_to_pdf,
    read_enhanced_metadata_from_pdf,
)


def create_mock_document(language: str = "en") -> Mock:
    """Create a mock semantic document for testing."""
    mock_doc = Mock()
    mock_doc.uuid = "12345678-1234-5678-9012-123456789abc"
    mock_doc.title = "Swiss Passport"
    mock_doc.formatted_title = "Swiss Passport"
    mock_doc.suffix = "renewal"
    mock_doc.title_custom = None

    # Mock document category
    mock_category = Mock()
    mock_category.name = "PASSPORT_CH"
    mock_category.id = "615"
    mock_doc.document_category = mock_category

    # Mock semantic pages with language
    mock_page = Mock()
    mock_page.lang = language
    mock_doc.semantic_pages = [mock_page]

    return mock_doc


def create_mock_document_categories():
    """Mock the document categories helper."""
    mock_category = Mock()
    mock_category.translated = lambda lang: {
        "de": "Schweizer Pass",
        "en": "Swiss Passport",
        "fr": "Passeport Suisse",
        "it": "Passaporto Svizzero",
    }[lang]

    return {"PASSPORT_CH": mock_category}


def test_pydantic_models():
    """Test Pydantic 2 models for data validation."""

    # Test PrefixedKeyword
    keyword = PrefixedKeyword(prefix="category", value="PASSPORT_CH")
    print(f"Prefixed keyword: {keyword}")

    # Test parsing from string
    parsed = PrefixedKeyword.from_string("suffix:renewal")
    print(f"Parsed keyword: {parsed}")

    # Test ParsedKeywords
    keywords_str = (
        "category:PASSPORT_CH, type:Swiss Passport, suffix:renewal, document, official"
    )
    parsed_keywords = ParsedKeywords.from_keywords_string(keywords_str)
    print(f"Structured metadata: {parsed_keywords.structured}")
    print(f"Searchable keywords: {parsed_keywords.searchable}")

    # Test EnhancedPdfMetadata
    metadata = EnhancedPdfMetadata(
        uuid="12345678-1234-5678-9012-123456789abc",
        title="Swiss Passport",
        document_category_key="PASSPORT_CH",
        document_category_name="Swiss Passport",
        document_category_title_de="Schweizer Pass",
        document_category_title_en="Swiss Passport",
        document_category_title_fr="Passeport Suisse",
        document_category_title_it="Passaporto Svizzero",
        title_suffix="renewal",
    )

    print(f"Full title: {metadata.get_full_title()}")
    print(f"Prefixed keywords: {metadata.create_prefixed_keywords()}")


def test_metadata_implementation():

    # Create test metadata
    metadata = EnhancedPdfMetadata(
        uuid="12345678-1234-5678-9012-123456789abc",
        title="Swiss Passport",
        document_category_key="PASSPORT_CH",
        document_category_name="Swiss Passport",
        document_category_title_de="Schweizer Pass",
        document_category_title_en="Swiss Passport",
        document_category_title_fr="Passeport Suisse",
        document_category_title_it="Passaporto Svizzero",
        title_suffix="renewal",
    )

    # Create PDF info properties
    pdf_info = create_document_info_properties_with_prefixed_keywords(metadata)
    print("Generated PDF info properties:")
    for key, value in pdf_info.items():
        print(f"  {key}: {value}")

    # Test parsing back
    parsed = parse_document_info_properties_with_prefixed_keywords(pdf_info)
    print("\nParsed metadata:")
    for key, value in parsed.items():
        print(f"  {key}: {value}")

    # Verify reliability
    assert parsed["document_category_key"] == "PASSPORT_CH"
    assert parsed["title_suffix"] == "renewal"
    print("\n✅ Option A parsing is reliable!")


def test_xmp_implementation():
    """Test XMP metadata implementation."""
    print("\n=== Testing XMP Metadata ===")

    # Create test metadata
    metadata = EnhancedPdfMetadata(
        uuid="12345678-1234-5678-9012-123456789abc",
        title="Swiss Passport",
        document_category_key="PASSPORT_CH",
        document_category_name="Swiss Passport",
        document_category_title_de="Schweizer Pass",
        document_category_title_en="Swiss Passport",
        document_category_title_fr="Passeport Suisse",
        document_category_title_it="Passaporto Svizzero",
        title_suffix="renewal",
    )

    # Create XMP metadata
    xmp_metadata = metadata.to_xmp_metadata()
    xmp_xml = xmp_metadata.to_xmp_xml()

    print("Generated XMP XML (first 500 chars):")
    print(xmp_xml[:500] + "..." if len(xmp_xml) > 500 else xmp_xml)

    # Verify XMP contains expected elements
    assert "hypodossier:DocumentCategoryKey" in xmp_xml
    assert "PASSPORT_CH" in xmp_xml
    assert "hypodossier:TitleSuffix" in xmp_xml
    assert "renewal" in xmp_xml
    print("\n✅ XMP metadata generation successful!")


def test_localized_title_generation():
    """Test that localized titles are generated correctly for different languages."""
    print("\n=== Testing Localized Title Generation ===")

    # Mock the document categories function
    import semantic_document.pdf_metadata_enhanced as pdf_module
    from semantic_document.pdf_metadata_enhanced import create_enhanced_pdf_metadata

    original_get_categories = pdf_module.get_document_categories_by_name
    pdf_module.get_document_categories_by_name = create_mock_document_categories

    try:
        # Test German localization
        mock_doc_de = create_mock_document(language="de")
        metadata_de = create_enhanced_pdf_metadata(mock_doc_de)
        expected_title_de = "615 Schweizer Pass renewal"
        assert metadata_de.title == expected_title_de
        assert metadata_de.document_category_name == "Schweizer Pass"
        print(f"✅ German title: {metadata_de.title}")

        # Test French localization
        mock_doc_fr = create_mock_document(language="fr")
        metadata_fr = create_enhanced_pdf_metadata(mock_doc_fr)
        expected_title_fr = "615 Passeport Suisse renewal"
        assert metadata_fr.title == expected_title_fr
        assert metadata_fr.document_category_name == "Passeport Suisse"
        print(f"✅ French title: {metadata_fr.title}")

        # Test Italian localization
        mock_doc_it = create_mock_document(language="it")
        metadata_it = create_enhanced_pdf_metadata(mock_doc_it)
        expected_title_it = "615 Passaporto Svizzero renewal"
        assert metadata_it.title == expected_title_it
        assert metadata_it.document_category_name == "Passaporto Svizzero"
        print(f"✅ Italian title: {metadata_it.title}")

        # Test English localization (default)
        mock_doc_en = create_mock_document(language="en")
        metadata_en = create_enhanced_pdf_metadata(mock_doc_en)
        expected_title_en = "615 Swiss Passport renewal"
        assert metadata_en.title == expected_title_en
        assert metadata_en.document_category_name == "Swiss Passport"
        print(f"✅ English title: {metadata_en.title}")

        # Test custom title (should override localization)
        mock_doc_custom = create_mock_document(language="de")
        mock_doc_custom.title_custom = "Custom Document Title"
        metadata_custom = create_enhanced_pdf_metadata(mock_doc_custom)
        assert metadata_custom.title == "Custom Document Title"
        print(f"✅ Custom title: {metadata_custom.title}")

    finally:
        # Restore original function
        pdf_module.get_document_categories_by_name = original_get_categories


def test_pdf_integration():
    """Test integration with actual PDF creation and reading."""
    print("\n=== Testing PDF Integration ===")

    # Mock the document categories function
    import semantic_document.pdf_metadata_enhanced as pdf_module

    original_get_categories = pdf_module.get_document_categories_by_name
    pdf_module.get_document_categories_by_name = create_mock_document_categories

    try:
        # Create mock document with German language
        mock_doc = create_mock_document(language="de")

        # Create a simple PDF
        writer = PdfWriter()

        # Add a blank page (required for valid PDF)
        from reportlab.pdfgen import canvas
        from io import BytesIO

        packet = BytesIO()
        c = canvas.Canvas(packet)
        c.drawString(100, 750, "Test Document")
        c.save()

        packet.seek(0)
        page_reader = PdfReader(packet)
        writer.add_page(page_reader.pages[0])

        # Add enhanced metadata
        add_enhanced_metadata_to_pdf(writer, mock_doc)

        # Write to temporary file
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmp_file:
            writer.write(tmp_file)
            tmp_path = Path(tmp_file.name)

        # Read back and verify
        with open(tmp_path, "rb") as f:
            reader = PdfReader(f)
            metadata = read_enhanced_metadata_from_pdf(reader)

        print("Read metadata from PDF:")
        print(f"  Standard metadata: {metadata['standard_metadata']}")
        print(f"  Option A metadata: {metadata['option_a_metadata']}")
        print(f"  XMP metadata available: {metadata['xmp_metadata'] is not None}")

        # Verify Option A data
        if metadata["option_a_metadata"]:
            option_a = metadata["option_a_metadata"]
            assert option_a["document_category_key"] == "PASSPORT_CH"
            assert option_a["title_suffix"] == "renewal"
            # Verify the localized title is used (should not be duplicated)
            assert (
                metadata["standard_metadata"]["/Title"] == "615 Schweizer Pass renewal"
            )
            print("✅ Option A metadata correctly written and read!")
            print(
                f"✅ Localized German title in PDF: {metadata['standard_metadata']['/Title']}"
            )

        # Verify XMP metadata content
        if metadata["xmp_metadata"]:
            xmp_meta = metadata["xmp_metadata"]
            # Check key XMP fields
            assert xmp_meta["dc_title"] == "615 Schweizer Pass renewal"
            assert xmp_meta["dc_creator"] == "Hypodossier AG"
            assert xmp_meta["hypodossier_documentcategorykey"] == "PASSPORT_CH"
            assert xmp_meta["hypodossier_titlesuffix"] == "renewal"
            assert xmp_meta["hypodossier_version"] == "1.0.0"
            assert "hypodossier_uuid" in xmp_meta
            print("✅ XMP metadata content verified!")
        else:
            assert False, "XMP metadata should be available"

        # Clean up
        tmp_path.unlink()

    finally:
        # Restore original function
        pdf_module.get_document_categories_by_name = original_get_categories


def test_suffix_comma_stripping():
    """Test that commas are stripped from title_suffix to prevent keyword parsing issues."""
    print("\n=== Testing Suffix Comma Stripping ===")

    # Test EnhancedPdfMetadata field validator
    metadata = EnhancedPdfMetadata(
        uuid="12345678-1234-5678-9012-123456789abc",
        title="Test Document",
        document_category_key="TEST_KEY",
        document_category_name="Test Category",
        document_category_title_de="Test DE",
        document_category_title_en="Test EN",
        document_category_title_fr="Test FR",
        document_category_title_it="Test IT",
        title_suffix="suffix, with, commas"
    )

    # Verify commas are stripped from title_suffix
    assert metadata.title_suffix == "suffix with commas"
    print(f"✅ Original suffix 'suffix, with, commas' cleaned to: '{metadata.title_suffix}'")

    # Test prefixed keywords don't contain problematic commas
    keywords = metadata.create_prefixed_keywords()
    suffix_keyword = next((k for k in keywords if k.startswith("suffix:")), None)
    assert suffix_keyword == "suffix:suffix with commas"
    print(f"✅ Suffix keyword: {suffix_keyword}")

    # Test that keywords can be properly parsed back
    keywords_string = ", ".join(keywords)
    parsed_keywords = ParsedKeywords.from_keywords_string(keywords_string)
    assert parsed_keywords.structured.get("suffix") == "suffix with commas"
    print(f"✅ Keywords string: {keywords_string}")
    print(f"✅ Parsed suffix: {parsed_keywords.structured.get('suffix')}")

    # Test with None suffix
    metadata_no_suffix = EnhancedPdfMetadata(
        uuid="12345678-1234-5678-9012-123456789abc",
        title="Test Document",
        document_category_key="TEST_KEY",
        document_category_name="Test Category",
        document_category_title_de="Test DE",
        document_category_title_en="Test EN",
        document_category_title_fr="Test FR",
        document_category_title_it="Test IT",
        title_suffix=None
    )
    assert metadata_no_suffix.title_suffix is None
    print("✅ None suffix handled correctly")


def test_edge_cases():
    """Test edge cases for localized title generation."""
    print("\n=== Testing Edge Cases ===")

    # Mock the document categories function
    import semantic_document.pdf_metadata_enhanced as pdf_module
    from semantic_document.pdf_metadata_enhanced import create_enhanced_pdf_metadata

    original_get_categories = pdf_module.get_document_categories_by_name
    pdf_module.get_document_categories_by_name = create_mock_document_categories

    try:
        # Test document without semantic pages (should use default language)
        mock_doc_no_pages = create_mock_document(language="de")
        mock_doc_no_pages.semantic_pages = []
        metadata_no_pages = create_enhanced_pdf_metadata(mock_doc_no_pages)
        # Should default to German since that's our fallback
        expected_title_default = "615 Schweizer Pass renewal"
        assert metadata_no_pages.title == expected_title_default
        print(f"✅ No pages fallback: {metadata_no_pages.title}")

        # Test document without suffix
        mock_doc_no_suffix = create_mock_document(language="fr")
        mock_doc_no_suffix.suffix = None
        metadata_no_suffix = create_enhanced_pdf_metadata(mock_doc_no_suffix)
        expected_title_no_suffix = "615 Passeport Suisse"
        assert metadata_no_suffix.title == expected_title_no_suffix
        print(f"✅ No suffix: {metadata_no_suffix.title}")

        # Test document with custom title (should ignore localization)
        mock_doc_custom = create_mock_document(language="it")
        mock_doc_custom.title_custom = "My Custom Document"
        metadata_custom = create_enhanced_pdf_metadata(mock_doc_custom)
        assert metadata_custom.title == "My Custom Document"
        assert (
            metadata_custom.document_category_name == "Passaporto Svizzero"
        )  # Category name should still be localized
        print(
            f"✅ Custom title with localized category: {metadata_custom.title} / {metadata_custom.document_category_name}"
        )

    finally:
        # Restore original function
        pdf_module.get_document_categories_by_name = original_get_categories


if __name__ == "__main__":
    test_pydantic_models()
    test_localized_title_generation()
    test_metadata_implementation()
    test_xmp_implementation()
    test_pdf_integration()
    test_edge_cases()
    print("\n🎉 All tests passed! Localized PDF titles are working correctly.")
