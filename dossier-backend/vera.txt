<?xml version="1.0" encoding="utf-8"?>
<report>
  <buildInformation>
    <releaseDetails id="core" version="1.26.2" buildDate="2024-12-04T11:35:00+01:00"></releaseDetails>
    <releaseDetails id="validation-model" version="1.26.2" buildDate="2024-12-06T09:04:00+01:00"></releaseDetails>
    <releaseDetails id="gui" version="1.26.4" buildDate="2024-12-06T11:43:00+01:00"></releaseDetails>
  </buildInformation>
  <jobsMay 27, 2025 7:56:47 PM org.verapdf.gf.model.impl.pd.GFPDMetadata getXMPPackage
WARNING: Problems with parsing metadata. Invalid attributes of rdf:RDF element
org.verapdf.xmp.XMPException: Invalid attributes of rdf:RDF element
	at org.verapdf.xmp.impl.ParseRDF.rdf_RDF(ParseRDF.java:127)
	at org.verapdf.xmp.impl.ParseRDF.parse(ParseRDF.java:105)
	at org.verapdf.xmp.impl.XMPMetaParser.parse(XMPMetaParser.java:111)
	at org.verapdf.xmp.XMPMetaFactory.parse(XMPMetaFactory.java:120)
	at org.verapdf.xmp.impl.VeraPDFMeta.parse(VeraPDFMeta.java:119)
	at org.verapdf.gf.model.impl.pd.GFPDMetadata.getXMPPackage(GFPDMetadata.java:103)
	at org.verapdf.gf.model.impl.pd.GFPDMetadata.getLinkedObjects(GFPDMetadata.java:90)
	at org.verapdf.pdfa.validation.validators.BaseValidator.addAllLinkedObjects(BaseValidator.java:232)
	at org.verapdf.pdfa.validation.validators.BaseValidator.checkNext(BaseValidator.java:199)
	at org.verapdf.pdfa.validation.validators.BaseValidator.validate(BaseValidator.java:144)
	at org.verapdf.pdfa.validation.validators.BaseValidator.validate(BaseValidator.java:108)
	at org.verapdf.processor.ProcessorImpl.validate(ProcessorImpl.java:244)
	at org.verapdf.processor.ProcessorImpl.process(ProcessorImpl.java:122)
	at org.verapdf.processor.BatchFileProcessor.processItem(BatchFileProcessor.java:167)
	at org.verapdf.processor.BatchFileProcessor.processList(BatchFileProcessor.java:85)
	at org.verapdf.processor.AbstractBatchProcessor.process(AbstractBatchProcessor.java:104)
	at org.verapdf.cli.VeraPdfCliProcessor.processFilePaths(VeraPdfCliProcessor.java:142)
	at org.verapdf.cli.VeraPdfCliProcessor.processPaths(VeraPdfCliProcessor.java:103)
	at org.verapdf.cli.VeraPdfCli.singleThreadProcess(VeraPdfCli.java:142)
	at org.verapdf.cli.VeraPdfCli.main(VeraPdfCli.java:111)
	at org.verapdf.apps.GreenfieldCliWrapper.main(GreenfieldCliWrapper.java:54)

>
    <job>
      <item size="480293">
        <name>/home/<USER>/Downloads/136-HBL Auftrag Finanzieren Verlängerung Andreas Dominik Wapf 1983-01-26.pdf</name>
      </item>
      <validationReport jobEndStatus="normal" profileName="PDF/A-1B validation profile" statement="PDF file is not compliant with Validation Profile requirements." isCompliant="false">
        <details passedRules="114" failedRules="14" passedChecks="7751" failedChecks="163">
          <rule specification="ISO 19005-1:2005" clause="6.1.11" testNumber="2" status="failed" failedChecks="1">
            <description>A file's name dictionary, as defined in PDF Reference 3.6.3, shall not contain the EmbeddedFiles key</description>
            <object>CosDocument</object>
            <test>containsEmbeddedFiles == false</test>
            <check status="failed">
              <context>root</context>
              <errorMessage>The document contains embedded files (EmbeddedFiles key is present in the file's name dictionary)</errorMessage>
            </check>
          </rule>
          <rule specification="ISO 19005-1:2005" clause="6.7.3" testNumber="5" status="failed" failedChecks="1">
            <description>The value of Keywords entry from the document information dictionary, if present, and its analogous XMP property "pdf:Keywords" shall be equivalent</description>
            <object>CosInfo</object>
            <test>Keywords == null || Keywords == XMPKeywords</test>
            <check status="failed">
              <context>root/trailer[0]/Info[0]</context>
              <errorMessage>The value of Keywords entry from the document Info dictionary and its matching XMP property "pdf:Keywords" are not equivalent (Info /Keywords = category:HBL_MORTGAGE_RENEWAL, id:f6c7a253-3a67-49cf-8eca-b9c21a1cedb1, suffix:Andreas Dominik Wapf 1983-01-26, XMP pdf:Keywords = null)</errorMessage>
            </check>
          </rule>
          <rule specification="ISO 19005-1:2005" clause="6.3.7" testNumber="1" status="failed" failedChecks="7">
            <description>All non-symbolic TrueType fonts shall specify MacRomanEncoding or WinAnsiEncoding, either as the value of the Encoding entry in the font dictionary or as the value of the BaseEncoding entry in the dictionary that is the value of the Encoding entry in the font dictionary. If the value of the Encoding entry is a dictionary, it shall not contain a Differences entry</description>
            <object>PDTrueTypeFont</object>
            <test>isSymbolic == true || ((Encoding == "MacRomanEncoding" || Encoding == "WinAnsiEncoding") &amp;&amp; containsDifferences == false)</test>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[7]/font[0](DefaultMetricsFont)</context>
              <errorMessage>A non-symbolic TrueType font encoding does not define a correct mapping to the Adobe Glyph List (Encoding = WinAnsiEncoding, Encoding entry contains a Differences = true)</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[12]/font[0](DefaultMetricsFont)</context>
              <errorMessage>A non-symbolic TrueType font encoding does not define a correct mapping to the Adobe Glyph List (Encoding = WinAnsiEncoding, Encoding entry contains a Differences = true)</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[25]/font[0](DefaultMetricsFontBold)</context>
              <errorMessage>A non-symbolic TrueType font encoding does not define a correct mapping to the Adobe Glyph List (Encoding = WinAnsiEncoding, Encoding entry contains a Differences = true)</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[110]/font[0](DefaultMetricsFont)</context>
              <errorMessage>A non-symbolic TrueType font encoding does not define a correct mapping to the Adobe Glyph List (Encoding = WinAnsiEncoding, Encoding entry contains a Differences = true)</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[113]/font[0](DefaultMetricsFontBold)</context>
              <errorMessage>A non-symbolic TrueType font encoding does not define a correct mapping to the Adobe Glyph List (Encoding = WinAnsiEncoding, Encoding entry contains a Differences = true)</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[729]/font[0](DefaultMetricsFontItalic)</context>
              <errorMessage>A non-symbolic TrueType font encoding does not define a correct mapping to the Adobe Glyph List (Encoding = WinAnsiEncoding, Encoding entry contains a Differences = true)</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[1284]/font[0](DefaultMetricsFontItalic)</context>
              <errorMessage>A non-symbolic TrueType font encoding does not define a correct mapping to the Adobe Glyph List (Encoding = WinAnsiEncoding, Encoding entry contains a Differences = true)</errorMessage>
            </check>
          </rule>
          <rule specification="ISO 19005-1:2005" clause="*******" testNumber="1" status="failed" failedChecks="143">
            <description>DeviceRGB may be used only if the file has a PDF/A-1 OutputIntent that uses an RGB colour space</description>
            <object>PDDeviceRGB</object>
            <test>gOutputCS != null &amp;&amp; gOutputCS == "RGB "</test>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[1]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[2]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[8]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[9]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[16]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[17]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[21]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[22]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[26]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[27]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[31]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[32]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[36]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[37]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[41]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[42]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[49]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[50]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[75]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[76]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[101]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[102]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[106]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[107]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[114]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[115]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[161]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[162]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[208]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[209]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[219]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[220]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[308]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[309]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[331]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[332]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[354]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[355]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[362]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[363]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[379]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[380]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[390]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[391]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[395]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[396]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[433]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[434]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[441]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[442]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[461]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[462]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[469]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[470]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[480]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[481]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[503]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[504]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[511]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[512]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[516]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[517]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[533]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[534]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[574]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[575]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[597]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[598]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[611]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[612]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[751]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[752]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[756]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[757]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[761]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[762]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[799]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[800]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[816]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[817]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[845]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[846]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[865]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[866]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[888]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[889]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[896]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[897]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[901]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[902]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[909]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[910]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[929]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[930]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[949]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[950]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[969]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[970]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[977]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[978]/colorSpace[0]</context>
              <errorMessage>DeviceRGB colour space is used without RGB output intent profile</errorMessage>
            </check>
          </rule>
          <rule specification="ISO 19005-1:2005" clause="6.1.11" testNumber="1" status="failed" failedChecks="2">
            <description>A file specification dictionary, as defined in PDF 3.10.2, shall not contain the EF key</description>
            <object>CosFileSpecification</object>
            <test>containsEF == false</test>
            <check status="failed">
              <context>root/EmbeddedFiles[0]</context>
              <errorMessage>A file specification dictionary contains the EF key</errorMessage>
            </check>
            <check status="failed">
              <context>root/indirectObjects[33](8 0)/directObject[0]/values[1]/values[0]/values[0]/elements[1]</context>
              <errorMessage>A file specification dictionary contains the EF key</errorMessage>
            </check>
          </rule>
          <rule specification="ISO 19005-1:2005" clause="6.2.4" testNumber="3" status="failed" failedChecks="1">
            <description>If an Image dictionary contains the Interpolate key, its value shall be false</description>
            <object>PDXImage</object>
            <test>Interpolate == false</test>
            <check status="failed">
              <context>root/document[0]/pages[0](3 0 obj PDPage)/contentStream[0]/operators[1476]/xObject[0]/contentStream[0](12 0 obj PDContentStream)/operators[2]/xObject[0](15 0 obj PDXImage)</context>
              <errorMessage>The value of the Interpolate key in the Image dictionary is true</errorMessage>
            </check>
          </rule>
          <rule specification="ISO 19005-1:2005" clause="6.7.11" testNumber="1" status="failed" failedChecks="1">
            <description>The PDF/A version and conformance level of a file shall be specified using the PDF/A Identification extension schema</description>
            <object>MainXMPPackage</object>
            <test>Identification_size == 1</test>
            <check status="failed">
              <context>root/document[0]/metadata[0](4 0 obj PDMetadata)/XMPPackage[0]</context>
              <errorMessage>The document metadata stream doesn't contains PDF/A Identification Schema</errorMessage>
            </check>
          </rule>
          <rule specification="ISO 19005-1:2005" clause="6.7.3" testNumber="7" status="failed" failedChecks="1">
            <description>The value of Producer entry from the document information dictionary, if present, and its analogous XMP property "pdf:Producer" shall be equivalent</description>
            <object>CosInfo</object>
            <test>Producer == null || Producer == XMPProducer</test>
            <check status="failed">
              <context>root/trailer[0]/Info[0]</context>
              <errorMessage>The value of Producer entry from the document Info dictionary and its matching XMP property "pdf:Producer" are not equivalent (Info /Producer = Dossier Manager/Content v1.0.0, XMP pdf:Producer = null)</errorMessage>
            </check>
          </rule>
          <rule specification="ISO 19005-1:2005" clause="6.7.3" testNumber="4" status="failed" failedChecks="1">
            <description>The value of Subject entry from the document information dictionary, if present, and its analogous XMP property "dc:description['x-default']" shall be equivalent</description>
            <object>CosInfo</object>
            <test>Subject == null || Subject == XMPDescription</test>
            <check status="failed">
              <context>root/trailer[0]/Info[0]</context>
              <errorMessage>The value of Subject entry from the document Info dictionary and its matching XMP property "dc:description['x-default']" are not equivalent (Info /Subject = Auftrag Finanzieren Verlängerung, XMP dc:description['x-default'] = null)</errorMessage>
            </check>
          </rule>
          <rule specification="ISO 19005-1:2005" clause="6.7.9" testNumber="1" status="failed" failedChecks="1">
            <description>The metadata stream shall conform to XMP Specification and well formed PDFAExtension Schema for all extensions</description>
            <object>XMPPackage</object>
            <test>isSerializationValid</test>
            <check status="failed">
              <context>root/document[0]/metadata[0](4 0 obj PDMetadata)/XMPPackage[0]</context>
              <errorMessage>The serialization of the metadata stream does not conform to XMP Specification</errorMessage>
            </check>
          </rule>
          <rule specification="ISO 19005-1:2005" clause="6.7.3" testNumber="2" status="failed" failedChecks="1">
            <description>The value of Title entry from the document information dictionary, if present, and its analogous XMP property "dc:title['x-default']" shall be equivalent</description>
            <object>CosInfo</object>
            <test>Title == null || Title == XMPTitle</test>
            <check status="failed">
              <context>root/trailer[0]/Info[0]</context>
              <errorMessage>The value of Title entry from the document Info dictionary and its matching XMP property "dc:title['x-default']" are not equivalent (Info /Title = 136-HBL Auftrag Finanzieren Verlängerung Andreas Dominik Wapf 1983-01-26, XMP dc:title['x-default'] = null)</errorMessage>
            </check>
          </rule>
          <rule specification="ISO 19005-1:2005" clause="6.7.3" testNumber="3" status="failed" failedChecks="1">
            <description>The value of Author entry from the document information dictionary, if present, and its analogous XMP property "dc:creator" shall be equivalent. dc:creator shall contain exactly one entry</description>
            <object>CosInfo</object>
            <test>Author == null || (Author == XMPCreator &amp;&amp; XMPCreatorSize == 1)</test>
            <check status="failed">
              <context>root/trailer[0]/Info[0]</context>
              <errorMessage>The value of Author entry from the document Info dictionary and its matching XMP property "dc:creator" are not equivalent (Info /Author = "", XMP dc:creator = null)</errorMessage>
            </check>
          </rule>
          <rule specification="ISO 19005-1:2005" clause="6.7.3" testNumber="6" status="failed" failedChecks="1">
            <description>The value of Creator entry from the document information dictionary, if present, and its analogous XMP property "xmp:CreatorTool" shall be equivalent</description>
            <object>CosInfo</object>
            <test>Creator == null || Creator == XMPCreatorTool</test>
            <check status="failed">
              <context>root/trailer[0]/Info[0]</context>
              <errorMessage>The value of Creator entry from the document Info dictionary and its matching XMP property "xmp:CreatorTool" are not equivalent (Info /Creator = Hypodossier AG, XMP xmp:CreatorTool = null)</errorMessage>
            </check>
          </rule>
          <rule specification="ISO 19005-1:2005" clause="6.1.3" testNumber="1" status="failed" failedChecks="1">
            <description>The file trailer dictionary shall contain the ID keyword. The file trailer referred to is either the last trailer dictionary in a PDF file, as described in PDF Reference 3.4.4 and 3.4.5, or the first page trailer in a linearized PDF file, as described in PDF Reference F.2</description>
            <object>CosDocument</object>
            <test>(isLinearized == true) ? (firstPageID != null) : (lastID != null)</test>
            <check status="failed">
              <context>root</context>
              <errorMessage>Missing ID in the document trailer</errorMessage>
            </check>
          </rule>
        </details>
      </validationReport>
      <duration start="1748368607542" finish="1748368608030">00:00:00.488</duration>
    </job>
  </jobs>
  <batchSummary totalJobs="1" failedToParse="0" encrypted="0" outOfMemory="0" veraExceptions="0">
    <validationReports compliant="0" nonCompliant="1" failedJobs="0">1</validationReports>
    <featureReports failedJobs="0">0</featureReports>
    <repairReports failedJobs="0">0</repairReports>
    <duration start="1748368607476" finish="1748368608073">00:00:00.597</duration>
  </batchSummary>
</report>
